{"name": "ant-design-x-test", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.1.1", "react-dom": "^19.1.1", "mind-elixir": "file:../lib/mind-elixir-v5", "@mind-elixir/node-menu": "file:../lib/@mind-elixir/node-menu-v5"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/node": "^20.5.1", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "less": "^4.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0"}}